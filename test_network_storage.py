#!/usr/bin/env python3
"""
Test script for network storage functionality in FSD API
"""

import requests
import json
import os
import tempfile
from pathlib import Path
from PIL import Image
import io

# API base URL
BASE_URL = "http://localhost:8000"

def create_test_image():
    """Create a simple test image for testing"""
    # Create a simple test image
    img = Image.new('RGB', (100, 100), color='red')
    img_bytes = io.BytesIO()
    img.save(img_bytes, format='JPEG')
    img_bytes.seek(0)
    return img_bytes

def test_health_endpoint():
    """Test the health endpoint"""
    print("Testing /health endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code == 200:
            print(f"✓ Health check passed: {response.json()}")
            return True
        else:
            print(f"✗ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ Health check request failed: {str(e)}")
        return False

def test_validate_storage_path(storage_path):
    """Test the storage path validation endpoint"""
    print(f"\nTesting path validation for: {storage_path}")
    
    try:
        response = requests.post(f"{BASE_URL}/validate-storage-path",
                               data={'storage_path': storage_path})
        
        if response.status_code == 200:
            result = response.json()
            print(f"✓ Path validation completed:")
            print(f"  Valid: {result['is_valid']}")
            print(f"  Absolute path: {result.get('absolute_path', 'N/A')}")
            print(f"  Is network path: {result.get('is_network_path', 'N/A')}")
            print(f"  Exists: {result.get('exists', 'N/A')}")
            if not result['is_valid']:
                print(f"  Error: {result.get('error_message', 'Unknown error')}")
            return result
        else:
            print(f"✗ Path validation failed: {response.status_code}")
            print(f"  Response: {response.text}")
            return None
    except Exception as e:
        print(f"✗ Path validation request failed: {str(e)}")
        return None

def test_process_with_storage_path(storage_path, test_description=""):
    """Test the /process endpoint with a specific storage path"""
    print(f"\nTesting /process with storage path: {storage_path}")
    if test_description:
        print(f"Description: {test_description}")
    
    # Create test image
    test_image = create_test_image()
    
    try:
        files = {'image': ('test.jpg', test_image, 'image/jpeg')}
        data = {
            'save_image': 'true',
            'storage_path': storage_path
        }
        
        response = requests.post(f"{BASE_URL}/process", files=files, data=data)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✓ Process completed successfully")
            
            if 'saved_image_path' in result:
                saved_path = result['saved_image_path']
                print(f"✓ Image saved to: {saved_path}")
                
                # Check if file actually exists
                if Path(saved_path).exists():
                    print(f"✓ Saved file verified to exist")
                    file_size = Path(saved_path).stat().st_size
                    print(f"  File size: {file_size} bytes")
                else:
                    print(f"✗ Saved file not found at: {saved_path}")
            else:
                print("ℹ No image was saved")
            
            return result
        else:
            print(f"✗ Process failed: {response.status_code}")
            error_detail = response.json().get('detail', 'Unknown error') if response.headers.get('content-type', '').startswith('application/json') else response.text
            print(f"  Error: {error_detail}")
            return None
    
    except Exception as e:
        print(f"✗ Request failed: {str(e)}")
        return None

def test_various_path_formats():
    """Test various path formats for validation"""
    print("\n" + "="*60)
    print("TESTING VARIOUS PATH FORMATS")
    print("="*60)
    
    test_paths = [
        # Local paths
        ("./test_local", "Local relative path"),
        (str(Path.cwd() / "test_absolute"), "Local absolute path"),
        
        # Temporary directory (should always work)
        (str(Path(tempfile.gettempdir()) / "fsd_api_test"), "System temp directory"),
        
        # Network paths (these may fail if not available)
        ("\\\\localhost\\c$\\temp", "UNC path to localhost (Windows)"),
        ("\\\\127.0.0.1\\c$\\temp", "UNC path with IP (Windows)"),
        ("\\\\nonexistent-server\\share", "Invalid UNC path"),
        
        # Invalid paths
        ("", "Empty path"),
        ("invalid:path", "Invalid path format"),
        ("/root/restricted", "Restricted path (likely no permission)"),
    ]
    
    results = []
    for path, description in test_paths:
        result = test_validate_storage_path(path)
        results.append((path, description, result))
    
    return results

def test_network_storage_scenarios():
    """Test realistic network storage scenarios"""
    print("\n" + "="*60)
    print("TESTING NETWORK STORAGE SCENARIOS")
    print("="*60)
    
    # Test with temp directory (should always work)
    temp_path = str(Path(tempfile.gettempdir()) / "fsd_api_network_test")
    print(f"\n1. Testing with system temp directory...")
    result = test_process_with_storage_path(temp_path, "System temporary directory - should work")
    
    # Test with current directory
    current_dir_path = str(Path.cwd() / "test_current_dir")
    print(f"\n2. Testing with current directory...")
    result = test_process_with_storage_path(current_dir_path, "Current working directory - should work")
    
    # Test with UNC path (Windows only, may fail)
    if os.name == 'nt':  # Windows
        print(f"\n3. Testing UNC path (Windows)...")
        unc_path = "\\\\localhost\\c$\\temp\\fsd_api_test"
        result = test_process_with_storage_path(unc_path, "UNC path to localhost - may work on Windows")
    
    # Test with invalid path
    print(f"\n4. Testing invalid path...")
    invalid_path = "\\\\nonexistent-server\\invalid\\path"
    result = test_process_with_storage_path(invalid_path, "Invalid network path - should fail gracefully")

def print_network_setup_tips():
    """Print tips for setting up network storage"""
    print("\n" + "="*60)
    print("NETWORK STORAGE SETUP TIPS")
    print("="*60)
    
    print("""
For testing network storage, you can:

1. LOCAL TESTING:
   - Use system temp directory: /tmp (Linux/Mac) or %TEMP% (Windows)
   - Use current working directory: ./test_images
   
2. WINDOWS NETWORK SHARES:
   - Map a network drive: net use Z: \\\\server\\share
   - Use UNC paths: \\\\server\\share\\path
   - Ensure the API service has network access permissions
   
3. LINUX/MAC NETWORK MOUNTS:
   - Mount SMB share: sudo mount -t cifs //server/share /mnt/point
   - Mount NFS share: sudo mount -t nfs server:/export /mnt/point
   - Use mounted paths: /mnt/nas_storage/images
   
4. NAS DEVICES:
   - Configure SMB/CIFS or NFS sharing
   - Create dedicated user account for API access
   - Test connectivity: ping nas-device-ip
   
5. TROUBLESHOOTING:
   - Check network connectivity
   - Verify permissions on network shares
   - Test with the /validate-storage-path endpoint
   - Check API server logs for detailed errors
""")

def main():
    """Main test function"""
    print("=== FSD API Network Storage Test ===\n")
    
    # Test basic connectivity
    if not test_health_endpoint():
        print("Health check failed, stopping tests")
        return
    
    # Test path validation with various formats
    validation_results = test_various_path_formats()
    
    # Test actual storage scenarios
    test_network_storage_scenarios()
    
    # Print setup tips
    print_network_setup_tips()
    
    # Summary
    print("\n" + "="*60)
    print("TEST SUMMARY")
    print("="*60)
    
    valid_paths = [r for _, _, r in validation_results if r and r.get('is_valid')]
    invalid_paths = [r for _, _, r in validation_results if r and not r.get('is_valid')]
    failed_validations = [r for _, _, r in validation_results if not r]
    
    print(f"✓ Valid paths found: {len(valid_paths)}")
    print(f"✗ Invalid paths: {len(invalid_paths)}")
    print(f"⚠ Failed validations: {len(failed_validations)}")
    
    if valid_paths:
        print(f"\nWorking storage paths:")
        for path, desc, result in validation_results:
            if result and result.get('is_valid'):
                print(f"  ✓ {path} ({desc})")
    
    print(f"\nFor production use:")
    print(f"  1. Use the /validate-storage-path endpoint to test paths")
    print(f"  2. Ensure proper network connectivity and permissions")
    print(f"  3. Consider using environment variables for default paths")
    print(f"  4. Monitor API logs for storage-related errors")

if __name__ == "__main__":
    main()
