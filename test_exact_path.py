#!/usr/bin/env python3
"""
Test script to demonstrate the exact path functionality.
This script shows how to use the API with exact file paths as provided by your colleague.
"""

import requests
import json
from pathlib import Path

# API base URL
API_BASE_URL = "http://localhost:8000"

def test_validate_exact_path():
    """Test the validation endpoint with an exact file path"""
    
    # Example exact path from your colleague
    exact_path = r"\\***********\nas02\20250626\19-26\19-26-0003.jpg"
    
    print(f"Testing validation for exact path: {exact_path}")
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/validate-storage-path",
            data={"storage_path": exact_path}
        )
        
        if response.status_code == 200:
            result = response.json()
            print("Validation result:")
            print(json.dumps(result, indent=2))
            return result["is_valid"]
        else:
            print(f"Validation failed with status {response.status_code}: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"Request failed: {e}")
        return False

def test_process_with_exact_path():
    """Test the process endpoint with an exact file path"""
    
    # Example exact path from your colleague
    exact_path = r"\\***********\nas02\20250626\19-26\19-26-0003.jpg"
    
    # Path to a test image file (you'll need to provide this)
    test_image_path = "test_image.jpg"  # Replace with actual test image path
    
    if not Path(test_image_path).exists():
        print(f"Test image not found: {test_image_path}")
        print("Please provide a test image file to run this test.")
        return
    
    print(f"Testing process endpoint with exact path: {exact_path}")
    
    try:
        with open(test_image_path, 'rb') as f:
            files = {'image': f}
            data = {
                'save_image': 'true',
                'storage_path': exact_path
            }
            
            response = requests.post(
                f"{API_BASE_URL}/process",
                files=files,
                data=data
            )
        
        if response.status_code == 200:
            result = response.json()
            print("Process result:")
            print(json.dumps(result, indent=2))
            
            if "saved_image_path" in result:
                print(f"\nImage saved to: {result['saved_image_path']}")
            else:
                print("\nNo image was saved (check save_image parameter)")
                
        else:
            print(f"Process failed with status {response.status_code}: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"Request failed: {e}")
    except FileNotFoundError:
        print(f"Test image file not found: {test_image_path}")

def test_vlm_with_exact_path():
    """Test the VLM endpoint with an exact file path"""
    
    # Example exact path from your colleague
    exact_path = r"\\***********\nas02\20250626\19-26\19-26-0003.jpg"
    
    # Path to a test image file (you'll need to provide this)
    test_image_path = "test_image.jpg"  # Replace with actual test image path
    
    if not Path(test_image_path).exists():
        print(f"Test image not found: {test_image_path}")
        print("Please provide a test image file to run this test.")
        return
    
    print(f"Testing VLM endpoint with exact path: {exact_path}")
    
    try:
        with open(test_image_path, 'rb') as f:
            files = {'file': f}
            data = {
                'prompt': 'Describe what you see in this image.',
                'save_image': 'true',
                'storage_path': exact_path
            }
            
            response = requests.post(
                f"{API_BASE_URL}/vlm",
                files=files,
                data=data
            )
        
        if response.status_code == 200:
            result = response.json()
            print("VLM result:")
            print(json.dumps(result, indent=2))
            
            if "saved_image_path" in result:
                print(f"\nImage saved to: {result['saved_image_path']}")
            else:
                print("\nNo image was saved (check save_image parameter)")
                
        else:
            print(f"VLM failed with status {response.status_code}: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"Request failed: {e}")
    except FileNotFoundError:
        print(f"Test image file not found: {test_image_path}")

def main():
    """Run all tests"""
    print("=== Testing Exact Path Functionality ===\n")
    
    print("1. Testing path validation...")
    is_valid = test_validate_exact_path()
    print(f"Path validation result: {'VALID' if is_valid else 'INVALID'}\n")
    
    if is_valid:
        print("2. Testing process endpoint...")
        test_process_with_exact_path()
        print()
        
        print("3. Testing VLM endpoint...")
        test_vlm_with_exact_path()
    else:
        print("Skipping API tests due to invalid path.")
        print("Note: This might be expected if the network path is not accessible from this machine.")

if __name__ == "__main__":
    main()
