# Exact Path Storage Usage

This document explains how to use the API with exact file paths as provided by your colleague.

## Overview

The API now supports both directory paths and complete file paths for the `storage_path` parameter. When your colleague provides an exact path like `\\***********\nas02\20250626\19-26\19-26-0003.jpg`, you can use it directly without needing to separate the directory and filename.

## Supported Path Formats

### 1. Directory Path (Original behavior)
```
storage_path: "\\***********\nas02\20250626\19-26\"
```
- The API will generate a unique filename and save the image in this directory
- Filename format: `{original_name}_{timestamp}.{extension}`

### 2. Complete File Path (New behavior)
```
storage_path: "\\***********\nas02\20250626\19-26\19-26-0003.jpg"
```
- The API will save the image with the exact filename specified
- No automatic filename generation

## API Endpoints

All endpoints that support image saving now accept both path formats:

### `/process` - Process all information at once
```bash
curl -X POST "http://localhost:8000/process" \
  -F "image=@your_image.jpg" \
  -F "save_image=true" \
  -F "storage_path=\\\\***********\\nas02\\20250626\\19-26\\19-26-0003.jpg"
```

### `/vlm` - Call VLM with custom prompt
```bash
curl -X POST "http://localhost:8000/vlm" \
  -F "file=@your_image.jpg" \
  -F "prompt=Describe this image" \
  -F "save_image=true" \
  -F "storage_path=\\\\***********\\nas02\\20250626\\19-26\\19-26-0003.jpg"
```

### `/validate-storage-path` - Validate path accessibility
```bash
curl -X POST "http://localhost:8000/validate-storage-path" \
  -F "storage_path=\\\\***********\\nas02\\20250626\\19-26\\19-26-0003.jpg"
```

## How It Works

1. **Path Detection**: The API automatically detects whether the provided path is a directory or complete file path by checking for a file extension.

2. **Directory Validation**: For complete file paths, the API validates that the parent directory is accessible and writable.

3. **File Saving**: 
   - For directory paths: Creates a unique filename and saves in the directory
   - For complete file paths: Saves with the exact filename specified

## Example Usage in Python

```python
import requests

# Using exact path from colleague
exact_path = r"\\***********\nas02\20250626\19-26\19-26-0003.jpg"

# Process an image with exact path
with open("test_image.jpg", "rb") as f:
    response = requests.post(
        "http://localhost:8000/process",
        files={"image": f},
        data={
            "save_image": "true",
            "storage_path": exact_path
        }
    )

result = response.json()
print(f"Image saved to: {result.get('saved_image_path')}")
```

## Response Format

When an image is saved, the response will include the `saved_image_path` field:

```json
{
  "project_title": "...",
  "drawing_title": "...",
  "drawing_number": "...",
  "drawing_titles_in_drawing": [...],
  "fsi_type": "...",
  "date_on_stamp": "...",
  "drawing_remark": "...",
  "saved_image_path": "\\\\***********\\nas02\\20250626\\19-26\\19-26-0003.jpg"
}
```

## Error Handling

The API will return appropriate error messages if:
- The network path is not accessible
- The parent directory doesn't exist or isn't writable
- There are permission issues
- The path format is invalid

## Testing

Use the provided `test_exact_path.py` script to test the functionality:

```bash
python test_exact_path.py
```

Make sure to:
1. Update the test image path in the script
2. Ensure your API server is running on `http://localhost:8000`
3. Have network access to the specified NAS path for full testing

## Notes

- The API maintains backward compatibility with directory paths
- Network paths (UNC paths) are fully supported
- Path validation occurs before attempting to save files
- The exact filename from your colleague will be preserved when using complete file paths
