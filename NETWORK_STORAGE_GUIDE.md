# Network Storage Guide

This guide covers how to use the FSD API with network storage, including NAS devices, shared drives, and remote file systems.

## Overview

The enhanced API now supports:
- ✅ **Local storage** (default behavior)
- ✅ **Network drives** (mapped drives on Windows, mounted filesystems on Linux)
- ✅ **UNC paths** (Windows network shares: `\\server\share\path`)
- ✅ **NAS storage** (when accessible via network protocols)
- ✅ **Path validation** before attempting to save
- ✅ **Permission checking** to ensure write access

## Supported Path Types

### Local Paths
```bash
# Windows
C:\images\converted
D:\storage\pdf_images

# Linux/Mac
/home/<USER>/images
/var/storage/converted_images
```

### Network Drives (Mapped/Mounted)
```bash
# Windows mapped drive
Z:\shared_storage\images

# Linux mounted network drive
/mnt/nas_storage/images
/media/shared/converted_images
```

### UNC Paths (Windows)
```bash
# Direct UNC path
\\nas-server\shared\images
\\192.168.1.100\storage\converted_images
\\file-server.company.com\departments\engineering\images
```

## Configuration Examples

### Environment Variables
```env
# Local storage
IMAGE_STORAGE_PATH=./local_images

# Network drive (Windows)
IMAGE_STORAGE_PATH=Z:\shared\images

# UNC path (Windows)
IMAGE_STORAGE_PATH=\\nas-server\storage\converted_images

# Mounted network storage (Linux)
IMAGE_STORAGE_PATH=/mnt/nas/converted_images
```

### Per-Request Configuration
```python
import requests

# Save to NAS via UNC path
response = requests.post("http://api-server:8000/process",
    files={'image': open('document.pdf', 'rb')},
    data={
        'save_image': 'true',
        'storage_path': '\\\\nas-server\\shared\\images'
    })

# Save to mounted network drive
response = requests.post("http://api-server:8000/process",
    files={'image': open('document.pdf', 'rb')},
    data={
        'save_image': 'true',
        'storage_path': '/mnt/company_nas/converted_images'
    })
```

## Path Validation

### Validate Before Use
```bash
# Test if a path is accessible
curl -X POST "http://localhost:8000/validate-storage-path" \
  -F "storage_path=\\\\nas-server\\shared\\images"
```

Response:
```json
{
  "path": "\\\\nas-server\\shared\\images",
  "is_valid": true,
  "absolute_path": "\\\\nas-server\\shared\\images",
  "error_message": null,
  "exists": true,
  "is_directory": true,
  "is_network_path": true,
  "parent_exists": true
}
```

### Common Validation Errors
```json
{
  "path": "\\\\invalid-server\\share",
  "is_valid": false,
  "absolute_path": null,
  "error_message": "Path validation failed: [Errno 2] No such file or directory"
}
```

## Network Setup Requirements

### Windows Environment

#### For UNC Paths:
1. **Network Discovery**: Enable network discovery on the API server
2. **Credentials**: Ensure the service account has access to network shares
3. **Firewall**: Allow SMB/CIFS traffic (ports 445, 139)

```powershell
# Test network connectivity
Test-NetConnection nas-server -Port 445

# Map drive for testing
net use Z: \\nas-server\shared /persistent:yes
```

#### For Service Accounts:
```powershell
# Run API service with network access
# Option 1: Use a domain account with network permissions
# Option 2: Configure "Log on as a service" rights for network access
```

### Linux Environment

#### Mount Network Shares:
```bash
# Install CIFS utilities
sudo apt-get install cifs-utils

# Create mount point
sudo mkdir -p /mnt/nas_storage

# Mount SMB/CIFS share
sudo mount -t cifs //nas-server/shared /mnt/nas_storage \
  -o username=apiuser,password=password,uid=1000,gid=1000

# Add to /etc/fstab for persistent mounting
echo "//nas-server/shared /mnt/nas_storage cifs username=apiuser,password=password,uid=1000,gid=1000 0 0" >> /etc/fstab
```

#### NFS Shares:
```bash
# Install NFS utilities
sudo apt-get install nfs-common

# Mount NFS share
sudo mount -t nfs nas-server:/export/storage /mnt/nas_storage

# Add to /etc/fstab
echo "nas-server:/export/storage /mnt/nas_storage nfs defaults 0 0" >> /etc/fstab
```

## Error Handling

### Common Network Errors

#### Connection Issues:
```json
{
  "error": "Storage configuration error: Path validation failed: [Errno 113] No route to host"
}
```
**Solution**: Check network connectivity and firewall settings

#### Permission Issues:
```json
{
  "error": "Storage configuration error: No write permission: [Errno 13] Permission denied"
}
```
**Solution**: Verify account permissions on the network share

#### Path Not Found:
```json
{
  "error": "Storage configuration error: Path validation failed: [Errno 2] No such file or directory"
}
```
**Solution**: Ensure the network path exists and is accessible

### Retry Logic (Client-Side)
```python
import requests
import time

def save_with_retry(file_path, storage_path, max_retries=3):
    for attempt in range(max_retries):
        try:
            response = requests.post("http://api-server:8000/process",
                files={'image': open(file_path, 'rb')},
                data={'save_image': 'true', 'storage_path': storage_path},
                timeout=30)
            
            if response.status_code == 200:
                return response.json()
            elif response.status_code == 400:
                # Path validation error - don't retry
                break
                
        except requests.exceptions.RequestException as e:
            if attempt < max_retries - 1:
                time.sleep(2 ** attempt)  # Exponential backoff
                continue
            raise
    
    return None
```

## Performance Considerations

### Network Storage Performance:
- **Local storage**: ~10-50ms per image
- **Gigabit network**: ~50-200ms per image
- **Slower networks**: May impact API response times

### Optimization Tips:
1. **Use local cache** for frequently accessed images
2. **Batch operations** when possible
3. **Monitor network latency** and adjust timeouts
4. **Consider async operations** for large files

## Security Best Practices

### Network Security:
1. **Use dedicated service accounts** with minimal required permissions
2. **Encrypt network traffic** (SMB3, NFS with Kerberos)
3. **Restrict network access** to specific IP ranges
4. **Monitor file access** logs

### Path Security:
```python
# The API automatically validates paths to prevent:
# - Directory traversal attacks (../)
# - Invalid path formats
# - Unauthorized access attempts
```

## Troubleshooting

### Debug Network Issues:
```bash
# Test path validation
curl -X POST "http://localhost:8000/validate-storage-path" \
  -F "storage_path=/your/network/path"

# Check API logs for detailed error messages
# Look for network timeouts, permission errors, or path issues
```

### Common Solutions:
1. **Mount issues**: Verify network share is properly mounted
2. **Permission errors**: Check service account permissions
3. **Timeout errors**: Increase network timeout settings
4. **Path format**: Use proper path separators for your OS

## Example Configurations

### Small Office Setup:
```env
# Simple NAS setup
IMAGE_STORAGE_PATH=\\\\office-nas\\shared\\api_images
SAVE_IMAGES=true
```

### Enterprise Setup:
```env
# Dedicated storage server
IMAGE_STORAGE_PATH=\\\\storage-cluster.company.com\\api_data\\converted_images
SAVE_IMAGES=true
```

### Development Setup:
```env
# Local development with optional network testing
IMAGE_STORAGE_PATH=./dev_images
SAVE_IMAGES=false
```
